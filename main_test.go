/*
Copyright 2020 The Kruise Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package main

import (
	"net"
	"os"
	"testing"
)

func TestGetWebhookBindAddress(t *testing.T) {
	tests := []struct {
		name         string
		webhookHost  string
		expectHost   bool
		expectNonZero bool
	}{
		{
			name:        "with WEBHOOK_HOST set to specific IP",
			webhookHost: "*************",
			expectHost:  true,
		},
		{
			name:        "with WEBHOOK_HOST set to 0.0.0.0 (e2e compatibility)",
			webhookHost: "0.0.0.0",
			expectHost:  true,
		},
		{
			name:        "with WEBHOOK_HOST set to localhost",
			webhookHost: "localhost",
			expectHost:  true,
		},
		{
			name:          "without WEBHOOK_HOST set (production case)",
			webhookHost:   "",
			expectNonZero: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set up environment
			if tt.webhookHost != "" {
				os.Setenv("WEBHOOK_HOST", tt.webhookHost)
			} else {
				os.Unsetenv("WEBHOOK_HOST")
			}
			defer os.Unsetenv("WEBHOOK_HOST")

			// Call the function
			result := getWebhookBindAddress()

			// Validate results
			if tt.expectHost {
				if result != tt.webhookHost {
					t.Errorf("Expected %s, got %s", tt.webhookHost, result)
				}
			}

			if tt.expectNonZero {
				// Should not be empty and should be a valid IP
				if result == "" {
					t.Error("Expected non-empty bind address")
				}
				
				// Should be a valid IP address
				ip := net.ParseIP(result)
				if ip == nil {
					t.Errorf("Expected valid IP address, got %s", result)
				}
				
				// Log the resolved address for debugging
				t.Logf("Resolved bind address: %s", result)
			}
		})
	}
}

// TestWebhookBindAddressE2ECompatibility tests that the function behaves correctly
// in scenarios similar to e2e test environments
func TestWebhookBindAddressE2ECompatibility(t *testing.T) {
	// Test case 1: E2E environment with WEBHOOK_HOST=0.0.0.0 (old behavior)
	t.Run("e2e with WEBHOOK_HOST=0.0.0.0", func(t *testing.T) {
		os.Setenv("WEBHOOK_HOST", "0.0.0.0")
		defer os.Unsetenv("WEBHOOK_HOST")
		
		result := getWebhookBindAddress()
		if result != "0.0.0.0" {
			t.Errorf("Expected 0.0.0.0 for e2e compatibility, got %s", result)
		}
	})
	
	// Test case 2: E2E environment without WEBHOOK_HOST (should not fail)
	t.Run("e2e without WEBHOOK_HOST", func(t *testing.T) {
		os.Unsetenv("WEBHOOK_HOST")
		
		result := getWebhookBindAddress()
		// Should return a valid IP address and not panic or exit
		if result == "" {
			t.Error("Expected non-empty result, got empty string")
		}
		
		ip := net.ParseIP(result)
		if ip == nil {
			t.Errorf("Expected valid IP address, got %s", result)
		}
		
		t.Logf("E2E test would use bind address: %s", result)
	})
}

// TestWebhookBindAddressNetworkCompatibility tests that the resolved addresses
// are actually bindable (important for e2e environments)
func TestWebhookBindAddressNetworkCompatibility(t *testing.T) {
	// Test that we can actually bind to the resolved address
	t.Run("can bind to resolved address", func(t *testing.T) {
		os.Unsetenv("WEBHOOK_HOST")

		bindAddr := getWebhookBindAddress()
		t.Logf("Testing bind to address: %s", bindAddr)

		// Try to create a listener on the resolved address
		listener, err := net.Listen("tcp", bindAddr+":0") // Use port 0 for any available port
		if err != nil {
			t.Errorf("Cannot bind to resolved address %s: %v", bindAddr, err)
			return
		}
		defer listener.Close()

		t.Logf("Successfully bound to %s", listener.Addr().String())
	})

	// Test that 0.0.0.0 fallback still works (e2e compatibility)
	t.Run("can bind to 0.0.0.0 fallback", func(t *testing.T) {
		// Try to bind to 0.0.0.0 to ensure e2e compatibility
		listener, err := net.Listen("tcp", "0.0.0.0:0")
		if err != nil {
			t.Errorf("Cannot bind to 0.0.0.0 (e2e fallback): %v", err)
			return
		}
		defer listener.Close()

		t.Logf("Successfully bound to 0.0.0.0 at %s", listener.Addr().String())
	})
}
